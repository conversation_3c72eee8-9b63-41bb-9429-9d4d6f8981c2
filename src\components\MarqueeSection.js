import React from "react";

const logos = [
  {
    name: "<PERSON>",
    svg: (
      <svg width="40" height="40" viewBox="0 0 40 40" fill="none"><circle cx="20" cy="20" r="20" fill="#23272a"/><ellipse cx="20" cy="20" rx="12" ry="8" fill="#b0b6c1"/></svg>
    ),
  },
  {
    name: "Epicurious",
    svg: (
      <svg width="40" height="40" viewBox="0 0 40 40" fill="none"><rect x="5" y="15" width="30" height="10" rx="5" fill="#b0b6c1"/><rect x="10" y="25" width="20" height="5" rx="2.5" fill="#23272a"/></svg>
    ),
  },
  {
    name: "GlobalBank",
    svg: (
      <svg width="40" height="40" viewBox="0 0 40 40" fill="none"><polygon points="20,5 35,35 5,35" fill="#b0b6c1"/><circle cx="20" cy="25" r="5" fill="#23272a"/></svg>
    ),
  },
  {
    name: "Catalog",
    svg: (
      <svg width="40" height="40" viewBox="0 0 40 40" fill="none"><circle cx="20" cy="20" r="18" fill="#b0b6c1"/><circle cx="20" cy="20" r="10" fill="#23272a"/></svg>
    ),
  },
  {
    name: "Luminous",
    svg: (
      <svg width="40" height="40" viewBox="0 0 40 40" fill="none"><rect x="10" y="10" width="20" height="5" rx="2.5" fill="#b0b6c1"/><rect x="10" y="20" width="20" height="5" rx="2.5" fill="#b0b6c1"/><rect x="10" y="30" width="20" height="5" rx="2.5" fill="#b0b6c1"/></svg>
    ),
  },
  {
    name: "Quotient",
    svg: (
      <svg width="40" height="40" viewBox="0 0 40 40" fill="none"><circle cx="20" cy="20" r="18" fill="#b0b6c1"/><rect x="18" y="10" width="4" height="20" rx="2" fill="#23272a"/></svg>
    ),
  },
];

export default function MarqueeSection() {
  return (
    <section className="w-full py-8 bg-gradient-to-b from-[#181d20] to-[#23272a] overflow-hidden">
      <div className="relative w-full">
        <div className="marquee flex items-center gap-16" style={{animation: 'marquee 24s linear infinite'}}>
          {Array(2).fill(null).map((_, i) => (
            <div className="flex items-center gap-16" key={i}>
              {logos.map((logo, idx) => (
                <div key={logo.name + i} className="flex items-center gap-3 min-w-[160px] px-2">
                  <div className="drop-shadow-lg">{logo.svg}</div>
                  <span className="text-gray-200 font-semibold text-lg tracking-wide" style={{textShadow: '0 2px 8px #181d20'}}> {logo.name} </span>
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
      <style>{`
        @keyframes marquee {
          0% { transform: translateX(0); }
          100% { transform: translateX(-50%); }
        }
        .marquee {
          width: 200%;
        }
      `}</style>
    </section>
  );
} 