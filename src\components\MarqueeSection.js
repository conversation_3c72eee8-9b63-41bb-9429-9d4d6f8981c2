import React from "react";

const features = [
  {
    name: "Real-Time Translation",
    icon: "🌐",
  },
  {
    name: "50+ Languages",
    icon: "🗣️",
  },
  {
    name: "99.9% Accuracy",
    icon: "🎯",
  },
  {
    name: "Voice Recognition",
    icon: "🎤",
  },
  {
    name: "Instant Processing",
    icon: "⚡",
  },
  {
    name: "Smart AI Engine",
    icon: "🧠",
  },
  {
    name: "Cross-Platform",
    icon: "📱",
  },
  {
    name: "Secure & Private",
    icon: "🔒",
  },
];

export default function MarqueeSection() {
  return (
    <section className="w-full py-12 bg-gradient-to-b from-[#181d20] to-[#23272a] overflow-hidden">
      <div className="text-center mb-8">
        <h3 className="text-2xl font-bold text-gray-100 mb-2">Powered by Advanced AI Technology</h3>
        <p className="text-gray-300">Experience the future of voice-to-translation with Kambaa AI</p>
      </div>
      <div className="relative w-full">
        <div className="marquee flex items-center gap-16" style={{animation: 'marquee 24s linear infinite'}}>
          {Array(2).fill(null).map((_, i) => (
            <div className="flex items-center gap-16" key={i}>
              {features.map((feature) => (
                <div key={feature.name + i} className="flex items-center gap-3 min-w-[200px] px-4">
                  <div className="text-3xl drop-shadow-lg">{feature.icon}</div>
                  <span className="text-gray-200 font-semibold text-lg tracking-wide whitespace-nowrap" style={{textShadow: '0 2px 8px #181d20'}}> {feature.name} </span>
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
      <style>{`
        @keyframes marquee {
          0% { transform: translateX(0); }
          100% { transform: translateX(-50%); }
        }
        .marquee {
          width: 200%;
        }
      `}</style>
    </section>
  );
}