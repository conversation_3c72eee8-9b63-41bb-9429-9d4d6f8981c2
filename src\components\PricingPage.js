"use client"
import React, { useState } from 'react';

const pricingPlans = [
  {
    id: 'basic',
    name: 'Basic',
    price: '$9',
    period: '/month',
    description: 'Perfect for individuals getting started with voice-to-text',
    features: [
      '5 hours of transcription per month',
      'Real-time voice recognition',
      '10+ languages supported',
      'Basic export formats (TXT, DOCX)',
      'Email support',
      'Standard accuracy (95%+)'
    ],
    buttonText: 'Start Basic',
    popular: false,
    icon: '🎤'
  },
  {
    id: 'advance',
    name: 'Advance',
    price: '$29',
    period: '/month',
    description: 'Ideal for professionals and content creators',
    features: [
      '50 hours of transcription per month',
      'Advanced AI with 99.9% accuracy',
      '50+ languages & dialects',
      'All export formats + API access',
      'Speaker identification',
      'Custom vocabulary training',
      'Priority support',
      'Noise reduction technology'
    ],
    buttonText: 'Go Advance',
    popular: true,
    icon: '🚀'
  },
  {
    id: 'community',
    name: 'Community',
    price: '$99',
    period: '/month',
    description: 'For teams and organizations with high-volume needs',
    features: [
      'Unlimited transcription',
      'Enterprise-grade security',
      'All languages supported',
      'White-label solutions',
      'Advanced analytics dashboard',
      'Custom integrations',
      'Dedicated account manager',
      'SLA guarantee',
      'Team collaboration tools'
    ],
    buttonText: 'Contact Sales',
    popular: false,
    icon: '🏢'
  }
];

export default function PricingPage() {
  const [isAnnual, setIsAnnual] = useState(false);

  const getPrice = (plan) => {
    if (plan.id === 'community') return plan.price;
    const monthlyPrice = parseInt(plan.price.replace('$', ''));
    const annualPrice = Math.floor(monthlyPrice * 12 * 0.8); // 20% discount
    return isAnnual ? `$${annualPrice}` : plan.price;
  };

  const getPeriod = (plan) => {
    if (plan.id === 'community') return plan.period;
    return isAnnual ? '/year' : plan.period;
  };

  return (
    <div className="min-h-screen py-24 px-4" style={{backgroundColor: 'var(--neutral-900)'}}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full mb-6 border" style={{backgroundColor: 'var(--primary-600)', borderColor: 'var(--primary-500)', color: 'white'}}>
            <span className="text-2xl">💎</span>
            <span className="text-sm font-semibold uppercase tracking-wider">Pricing Plans</span>
          </div>
          
          <h1 className="font-bold mb-6 leading-tight" style={{fontSize: 'var(--text-5xl)', color: 'var(--neutral-100)'}}>
            Choose Your Perfect{' '}
            <span style={{color: 'var(--primary-400)'}}>Kambaa AI</span> Plan
          </h1>
          
          <p className="max-w-2xl mx-auto leading-relaxed mb-8" style={{fontSize: 'var(--text-xl)', color: 'var(--neutral-300)'}}>
            Transform your voice into perfect text with our advanced AI technology. Select the plan that fits your needs.
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center gap-4 mb-12">
            <span className={`text-sm font-medium ${!isAnnual ? 'text-white' : 'text-gray-400'}`}>Monthly</span>
            <button
              onClick={() => setIsAnnual(!isAnnual)}
              className="relative w-14 h-7 rounded-full transition-colors duration-300"
              style={{backgroundColor: isAnnual ? 'var(--primary-600)' : 'var(--neutral-700)'}}
            >
              <div className={`absolute top-1 w-5 h-5 bg-white rounded-full transition-transform duration-300 ${isAnnual ? 'translate-x-7' : 'translate-x-1'}`}></div>
            </button>
            <span className={`text-sm font-medium ${isAnnual ? 'text-white' : 'text-gray-400'}`}>
              Annual <span className="text-green-400 text-xs">(Save 20%)</span>
            </span>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {pricingPlans.map((plan) => (
            <div
              key={plan.id}
              className={`relative rounded-3xl p-8 border transition-all duration-300 hover:scale-105 ${
                plan.popular ? 'ring-2 ring-primary-400 scale-105' : ''
              }`}
              style={{
                backgroundColor: plan.popular ? 'var(--neutral-800)' : 'var(--neutral-850)',
                borderColor: plan.popular ? 'var(--primary-400)' : 'var(--neutral-700)',
                background: plan.popular 
                  ? 'linear-gradient(135deg, var(--neutral-800), var(--neutral-750))' 
                  : 'var(--neutral-800)'
              }}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="px-4 py-2 rounded-full text-sm font-semibold text-white" style={{backgroundColor: 'var(--primary-600)'}}>
                    Most Popular
                  </div>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center mb-8">
                <div className="text-4xl mb-4">{plan.icon}</div>
                <h3 className="text-2xl font-bold mb-2" style={{color: 'var(--neutral-100)'}}>
                  {plan.name}
                </h3>
                <p className="text-sm mb-6" style={{color: 'var(--neutral-400)'}}>
                  {plan.description}
                </p>
                
                {/* Price */}
                <div className="mb-6">
                  <span className="text-5xl font-bold" style={{color: 'var(--neutral-100)'}}>
                    {getPrice(plan)}
                  </span>
                  <span className="text-lg" style={{color: 'var(--neutral-400)'}}>
                    {getPeriod(plan)}
                  </span>
                </div>

                {/* CTA Button */}
                <button
                  className={`w-full py-3 px-6 rounded-xl font-semibold transition-all duration-300 ${
                    plan.popular 
                      ? 'text-white hover:scale-105' 
                      : 'border-2 hover:scale-105'
                  }`}
                  style={{
                    backgroundColor: plan.popular ? 'var(--primary-600)' : 'transparent',
                    borderColor: plan.popular ? 'var(--primary-600)' : 'var(--neutral-600)',
                    color: plan.popular ? 'white' : 'var(--neutral-300)'
                  }}
                >
                  {plan.buttonText}
                </button>
              </div>

              {/* Features */}
              <div className="space-y-4">
                <h4 className="font-semibold text-sm uppercase tracking-wider" style={{color: 'var(--neutral-400)'}}>
                  What's Included:
                </h4>
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <svg className="w-5 h-5 mt-0.5 flex-shrink-0" style={{color: 'var(--primary-400)'}} viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                      </svg>
                      <span className="text-sm leading-relaxed" style={{color: 'var(--neutral-300)'}}>
                        {feature}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-lg mb-4" style={{color: 'var(--neutral-400)'}}>
            Need a custom solution? 
          </p>
          <button className="px-8 py-3 rounded-xl border-2 font-semibold transition-all duration-300 hover:scale-105" style={{borderColor: 'var(--primary-400)', color: 'var(--primary-400)'}}>
            Contact Our Sales Team
          </button>
        </div>
      </div>
    </div>
  );
}
