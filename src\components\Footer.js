"use client"
import React, { useState } from 'react';

export default function Footer() {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubscribe = (e) => {
    e.preventDefault();
    if (email) {
      setIsSubscribed(true);
      setEmail('');
      setTimeout(() => setIsSubscribed(false), 3000);
    }
  };

  return (
    <footer className="w-full py-16 px-4 border-t" style={{backgroundColor: 'var(--neutral-900)', borderColor: 'var(--neutral-800)'}}>
      <div className="max-w-6xl mx-auto">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 mb-12">
          {/* Logo and Description */}
          <div className="lg:col-span-1">
            <div className="flex items-center gap-2 mb-6">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center" style={{backgroundColor: 'var(--primary-600)'}}>
                <span className="text-white font-bold text-lg">K</span>
              </div>
              <span className="font-bold text-2xl" style={{color: 'var(--neutral-100)'}}>Kambaa AI</span>
            </div>
            
            <p className="mb-6 leading-relaxed" style={{fontSize: 'var(--text-base)', color: 'var(--neutral-300)'}}>
              Transform your audio into perfect text with our advanced AI-powered transcription service. 
              Fast, accurate, and secure - trusted by professionals worldwide.
            </p>

            {/* Social Links */}
            <div className="flex gap-4">
              <a href="#" className="w-10 h-10 rounded-lg flex items-center justify-center transition-colors duration-300 hover:bg-opacity-80" style={{backgroundColor: 'var(--neutral-800)', color: 'var(--neutral-400)'}}>
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                </svg>
              </a>
              <a href="#" className="w-10 h-10 rounded-lg flex items-center justify-center transition-colors duration-300 hover:bg-opacity-80" style={{backgroundColor: 'var(--neutral-800)', color: 'var(--neutral-400)'}}>
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
              <a href="#" className="w-10 h-10 rounded-lg flex items-center justify-center transition-colors duration-300 hover:bg-opacity-80" style={{backgroundColor: 'var(--neutral-800)', color: 'var(--neutral-400)'}}>
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                </svg>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="lg:col-span-1">
            <h3 className="font-semibold mb-6" style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-100)'}}>
              Quick Links
            </h3>
            <ul className="space-y-4">
              <li>
                <a href="#" className="transition-colors duration-300 hover:text-primary-400" style={{fontSize: 'var(--text-base)', color: 'var(--neutral-400)'}}>
                  How it Works
                </a>
              </li>
              <li>
                <a href="#" className="transition-colors duration-300 hover:text-primary-400" style={{fontSize: 'var(--text-base)', color: 'var(--neutral-400)'}}>
                  Pricing
                </a>
              </li>
              {/* <li>
                <a href="#" className="transition-colors duration-300 hover:text-primary-400" style={{fontSize: 'var(--text-base)', color: 'var(--neutral-400)'}}>
                  API Documentation
                </a>
              </li> */}
              <li>
                <a href="#" className="transition-colors duration-300 hover:text-primary-400" style={{fontSize: 'var(--text-base)', color: 'var(--neutral-400)'}}>
                  Support
                </a>
              </li>
              {/* <li>
                <a href="#" className="transition-colors duration-300 hover:text-primary-400" style={{fontSize: 'var(--text-base)', color: 'var(--neutral-400)'}}>
                  Privacy Policy
                </a>
              </li> */}
            </ul>
          </div>

          {/* Newsletter Subscription */}
          <div className="lg:col-span-1">
            <h3 className="font-semibold mb-6" style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-100)'}}>
              Stay Updated
            </h3>
            <p className="mb-6 leading-relaxed" style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-400)'}}>
              Get the latest updates on new features, improvements, and AI transcription tips.
            </p>
            
            <form onSubmit={handleSubscribe} className="space-y-4">
              <div className="relative">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 transition-all duration-300"
                  style={{
                    backgroundColor: 'var(--neutral-800)',
                    borderColor: 'var(--neutral-700)',
                    color: 'var(--neutral-100)',
                    focusRingColor: 'var(--primary-500)'
                  }}
                  required
                />
              </div>
              
              <button
                type="submit"
                disabled={isSubscribed}
                className="w-full btn-primary py-3 rounded-lg font-semibold transition-all duration-300 disabled:opacity-50"
              >
                {isSubscribed ? '✓ Subscribed!' : 'Subscribe'}
              </button>
            </form>

            {isSubscribed && (
              <p className="mt-3 text-sm" style={{color: 'var(--primary-400)'}}>
                Thank you for subscribing! 🎉
              </p>
            )}
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="pt-8 border-t flex flex-col md:flex-row justify-between items-center gap-4" style={{borderColor: 'var(--neutral-800)'}}>
          <p style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-500)'}}>
            © 2024 Kambaa AI. All rights reserved.
          </p>
          
          <div className="flex items-center gap-6">
            <a href="#" className="transition-colors duration-300 hover:text-primary-400" style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-500)'}}>
              Terms of Service
            </a>
            <a href="#" className="transition-colors duration-300 hover:text-primary-400" style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-500)'}}>
              Privacy Policy
            </a>
            <a href="#" className="transition-colors duration-300 hover:text-primary-400" style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-500)'}}>
              Cookie Policy
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}
