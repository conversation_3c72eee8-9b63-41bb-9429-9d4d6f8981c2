import React from "react";

const features = [
  "Real-time", "Accurate", "Multi-language", "Export", "Secure", "Speaker ID", "Cloud Sync", "Accessible"
];

const trustedBy = [
  "Retwist", "Truce", "Vertigo"
];

export default function BentoGridSection() {
  return (
    <section className="w-full flex flex-col items-center justify-center py-16 px-2 md:px-8 bg-transparent">
      <div className="grid grid-cols-1 md:grid-cols-4 grid-rows-6 md:grid-rows-3 gap-4 w-full max-w-6xl auto-rows-[minmax(120px,auto)]">
        {/* Headline & Subheadline */}
        <div className="col-span-2 row-span-1 bg-gradient-to-b from-[#23272a] to-[#181d20] rounded-2xl p-6 flex flex-col justify-between shadow text-left">
          <div>
            <h2 className="text-2xl md:text-3xl font-bold text-gray-100 mb-1">Innovative AI,<br />tailored for you</h2>
            <p className="text-gray-300 text-sm md:text-base">Bringing your voice to life with cutting-edge speech-to-text technology.</p>
          </div>
        </div>
        {/* Callout top right */}
        <div className="col-span-2 row-span-1 bg-transparent flex flex-col items-end justify-between p-4">
          <span className="text-xs text-gray-400">Bringing your voice to life with AI.</span>
        </div>
        {/* Feature tags */}
        <div className="col-span-2 row-span-1 bg-gradient-to-b from-[#23272a] to-[#181d20] rounded-2xl p-4 flex flex-wrap gap-2 items-center shadow">
          {features.map((f, i) => (
            <span key={i} className="bg-[#181d20] text-gray-200 px-3 py-1 rounded-full text-xs font-medium border border-gray-700/60">{f}</span>
          ))}
        </div>
        {/* Image cell */}
        <div className="col-span-1 row-span-2 bg-[#23272a] rounded-2xl overflow-hidden flex items-center justify-center shadow">
          <img src="https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80" alt="AI Voice" className="object-cover w-full h-full" />
        </div>
        {/* Unlimited callout */}
        <div className="col-span-1 row-span-1 bg-gradient-to-b from-indigo-900 to-[#23272a] rounded-2xl flex items-center justify-center shadow">
          <span className="text-indigo-200 font-bold text-lg text-center">UNLIMITED<br />TRANSCRIPTIONS</span>
        </div>
        {/* Testimonial */}
        <div className="col-span-2 row-span-2 bg-gradient-to-b from-[#23272a] to-[#181d20] rounded-2xl p-6 flex flex-col justify-between shadow">
          <p className="text-gray-100 text-base mb-4">“The flexibility and efficiency of this AI tool are unmatched. It handles everything from accents to noisy environments, delivering accurate results. A game-changer for productivity!”</p>
          <div className="flex items-center gap-3 mt-auto">
            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" className="w-8 h-8 rounded-full object-cover border-2 border-gray-700" />
            <div>
              <div className="text-gray-100 font-semibold text-xs">Alex Kim</div>
              <div className="text-gray-400 text-xs">Content Creator</div>
            </div>
          </div>
        </div>
        {/* Stat: Accuracy */}
        <div className="col-span-1 row-span-1 bg-gradient-to-b from-[#23272a] to-[#181d20] rounded-2xl flex flex-col items-center justify-center shadow">
          <span className="text-3xl font-bold text-indigo-200 mb-1">90%+</span>
          <span className="text-gray-400 text-xs">Accuracy</span>
        </div>
        {/* Trusted by */}
        <div className="col-span-2 row-span-1 bg-gradient-to-b from-[#23272a] to-[#181d20] rounded-2xl flex flex-col items-center justify-center shadow">
          <span className="text-gray-400 text-xs mb-2">Trusted by</span>
          <div className="flex gap-4">
            {trustedBy.map((name, i) => (
              <span key={i} className="text-gray-200 font-semibold text-base">{name}</span>
            ))}
          </div>
        </div>
        {/* Stat: Fast Turnaround */}
        <div className="col-span-1 row-span-1 bg-gradient-to-b from-indigo-900 to-[#23272a] rounded-2xl flex flex-col items-center justify-center shadow">
          <span className="text-xs text-gray-400 mb-1">FAST</span>
          <span className="text-lg font-bold text-indigo-200">TURNAROUND</span>
        </div>
        {/* Image cell 2 */}
        <div className="col-span-1 row-span-2 bg-[#23272a] rounded-2xl overflow-hidden flex items-center justify-center shadow">
          <img src="https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=400&q=80" alt="AI Mobile" className="object-cover w-full h-full" />
        </div>
      </div>
    </section>
  );
} 