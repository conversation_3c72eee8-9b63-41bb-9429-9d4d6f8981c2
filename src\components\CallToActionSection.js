import React from 'react';

export default function CallToActionSection() {
  return (
    <section className="w-full py-24 px-4 relative overflow-hidden" style={{backgroundColor: 'var(--neutral-900)'}}>
      {/* Background decorative elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 rounded-full blur-xl animate-pulse" style={{backgroundColor: 'var(--primary-500)', opacity: 0.1}}></div>
        <div className="absolute bottom-20 right-10 w-48 h-48 rounded-full blur-xl animate-pulse delay-1000" style={{backgroundColor: 'var(--secondary-500)', opacity: 0.1}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full blur-3xl" style={{backgroundColor: 'var(--primary-600)', opacity: 0.05}}></div>
      </div>

      <div className="max-w-4xl mx-auto text-center relative z-10">
        {/* Badge */}
        <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full mb-8 border" style={{backgroundColor: 'var(--primary-600)', opacity: 0.2, borderColor: 'var(--primary-500)', color: 'var(--primary-300)'}}>
          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
          <span className="text-sm font-semibold uppercase tracking-wider">Start Your Free Trial</span>
        </div>

        {/* Main heading */}
        <h2 className="font-bold mb-6 leading-tight" style={{fontSize: 'var(--text-5xl)', color: 'var(--neutral-100)'}}>
          Ready to Transform Your{' '}
          <span style={{color: 'var(--primary-400)'}}>Audio</span>{' '}
          Into Perfect Text?
        </h2>

        {/* Description */}
        <p className="mb-12 max-w-2xl mx-auto leading-relaxed" style={{fontSize: 'var(--text-xl)', color: 'var(--neutral-300)'}}>
          Join thousands of professionals who trust ZINLE AI for accurate, fast, and secure audio transcription. 
          Start your free trial today and experience the future of speech-to-text technology.
        </p>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
          <button className="btn-primary text-lg px-10 py-4 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
            Start Free Trial - No Credit Card Required
          </button>
          <button className="btn-secondary text-lg px-10 py-4 rounded-lg">
            Schedule a Demo
          </button>
        </div>

        {/* Features grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
          <div className="flex flex-col items-center text-center">
            <div className="w-16 h-16 rounded-2xl flex items-center justify-center mb-4" style={{backgroundColor: 'var(--primary-600)', opacity: 0.2}}>
              <svg className="w-8 h-8" style={{color: 'var(--primary-400)'}} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M9 12l2 2 4-4"/>
                <circle cx="12" cy="12" r="10"/>
              </svg>
            </div>
            <h3 className="font-semibold mb-2" style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-100)'}}>
              99.9% Accuracy
            </h3>
            <p style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-400)'}}>
              Industry-leading precision with advanced AI
            </p>
          </div>

          <div className="flex flex-col items-center text-center">
            <div className="w-16 h-16 rounded-2xl flex items-center justify-center mb-4" style={{backgroundColor: 'var(--secondary-600)', opacity: 0.2}}>
              <svg className="w-8 h-8" style={{color: 'var(--secondary-400)'}} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"/>
              </svg>
            </div>
            <h3 className="font-semibold mb-2" style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-100)'}}>
              Lightning Fast
            </h3>
            <p style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-400)'}}>
              Real-time transcription in seconds
            </p>
          </div>

          <div className="flex flex-col items-center text-center">
            <div className="w-16 h-16 rounded-2xl flex items-center justify-center mb-4" style={{backgroundColor: 'var(--primary-600)', opacity: 0.2}}>
              <svg className="w-8 h-8" style={{color: 'var(--primary-400)'}} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                <circle cx="12" cy="16" r="1"/>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
              </svg>
            </div>
            <h3 className="font-semibold mb-2" style={{fontSize: 'var(--text-lg)', color: 'var(--neutral-100)'}}>
              Secure & Private
            </h3>
            <p style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-400)'}}>
              End-to-end encryption for your data
            </p>
          </div>
        </div>

        {/* Trust indicators */}
        <div className="mt-16 pt-8 border-t" style={{borderColor: 'var(--neutral-700)'}}>
          <p className="mb-6" style={{fontSize: 'var(--text-sm)', color: 'var(--neutral-400)'}}>
            Trusted by 100,000+ users worldwide
          </p>
          <div className="flex justify-center items-center gap-8 opacity-60">
            <div className="text-2xl font-bold" style={{color: 'var(--neutral-500)'}}>Microsoft</div>
            <div className="text-2xl font-bold" style={{color: 'var(--neutral-500)'}}>Google</div>
            <div className="text-2xl font-bold" style={{color: 'var(--neutral-500)'}}>Amazon</div>
            <div className="text-2xl font-bold" style={{color: 'var(--neutral-500)'}}>Apple</div>
          </div>
        </div>
      </div>
    </section>
  );
}
