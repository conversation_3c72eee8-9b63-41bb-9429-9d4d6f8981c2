"use client"
import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
gsap.registerPlugin(ScrollTrigger);

const slides = [
  {
    title: "Enterprise-Ready Security",
    desc: "Your audio and transcripts are protected with industry-leading encryption and privacy controls. Trusted by top organizations worldwide.",
    cta: "Learn More",
    logos: [
      { name: "<PERSON>", svg: <svg width="36" height="36" viewBox="0 0 40 40" fill="none"><circle cx="20" cy="20" r="20" fill="#23272a"/><ellipse cx="20" cy="20" rx="12" ry="8" fill="#b0b6c1"/></svg> },
      { name: "Epicurious", svg: <svg width="36" height="36" viewBox="0 0 40 40" fill="none"><rect x="5" y="15" width="30" height="10" rx="5" fill="#b0b6c1"/><rect x="10" y="25" width="20" height="5" rx="2.5" fill="#23272a"/></svg> },
      { name: "GlobalBank", svg: <svg width="36" height="36" viewBox="0 0 40 40" fill="none"><polygon points="20,5 35,35 5,35" fill="#b0b6c1"/><circle cx="20" cy="25" r="5" fill="#23272a"/></svg> },
    ],
    animation: (
      <svg width="320" height="320" viewBox="0 0 320 320" fill="none"><circle cx="160" cy="160" r="120" fill="#23272a"/><rect x="100" y="140" width="120" height="40" rx="20" fill="#6366f1" opacity="0.7" /><rect x="120" y="160" width="80" height="20" rx="10" fill="#b0b6c1" opacity="0.5" /></svg>
    ),
  },
  {
    title: "Loved by Creators",
    desc: "Content creators, podcasters, and educators rely on our AI to turn their voice into polished, shareable text. Join a global community of storytellers.",
    cta: "See User Stories",
    logos: [
      { name: "Catalog", svg: <svg width="36" height="36" viewBox="0 0 40 40" fill="none"><circle cx="20" cy="20" r="18" fill="#b0b6c1"/><circle cx="20" cy="20" r="10" fill="#23272a"/></svg> },
      { name: "Luminous", svg: <svg width="36" height="36" viewBox="0 0 40 40" fill="none"><rect x="10" y="10" width="20" height="5" rx="2.5" fill="#b0b6c1"/><rect x="10" y="20" width="20" height="5" rx="2.5" fill="#b0b6c1"/><rect x="10" y="30" width="20" height="5" rx="2.5" fill="#b0b6c1"/></svg> },
      { name: "Quotient", svg: <svg width="36" height="36" viewBox="0 0 40 40" fill="none"><circle cx="20" cy="20" r="18" fill="#b0b6c1"/><rect x="18" y="10" width="4" height="20" rx="2" fill="#23272a"/></svg> },
    ],
    animation: (
      <svg width="320" height="320" viewBox="0 0 320 320" fill="none"><rect x="60" y="60" width="200" height="200" rx="40" fill="#6366f1" opacity="0.7" /><circle cx="160" cy="160" r="60" fill="#b0b6c1" opacity="0.5" /></svg>
    ),
  },
  {
    title: "Seamless Integrations",
    desc: "Connect with your favorite tools—export transcripts to Google Docs, Slack, Notion, and more. Workflows that fit your needs.",
    cta: "Explore Integrations",
    logos: [
      { name: "Slack", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><circle cx='20' cy='20' r='20' fill='#b0b6c1'/><rect x='10' y='18' width='20' height='4' rx='2' fill='#23272a'/></svg> },
      { name: "Notion", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><rect x='8' y='8' width='24' height='24' rx='6' fill='#b0b6c1'/><rect x='14' y='14' width='12' height='12' rx='3' fill='#23272a'/></svg> },
      { name: "Google Docs", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><rect x='8' y='8' width='24' height='24' rx='6' fill='#b0b6c1'/><rect x='14' y='14' width='12' height='12' rx='3' fill='#23272a'/></svg> },
    ],
    animation: (
      <svg width="320" height="320" viewBox="0 0 320 320" fill="none"><rect x="100" y="100" width="120" height="120" rx="30" fill="#6366f1" opacity="0.7" /><rect x="120" y="120" width="80" height="80" rx="20" fill="#b0b6c1" opacity="0.5" /></svg>
    ),
  },
  {
    title: "Global Language Support",
    desc: "Transcribe and translate in 30+ languages. Our AI breaks down barriers and connects you to the world.",
    cta: "See Supported Languages",
    logos: [
      { name: "World", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><circle cx='20' cy='20' r='18' fill='#b0b6c1'/><ellipse cx='20' cy='20' rx='10' ry='6' fill='#23272a'/></svg> },
      { name: "Translate", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><rect x='8' y='8' width='24' height='24' rx='6' fill='#b0b6c1'/><rect x='14' y='14' width='12' height='12' rx='3' fill='#23272a'/></svg> },
      { name: "Globe", svg: <svg width='36' height='36' viewBox='0 0 40 40' fill='none'><circle cx='20' cy='20' r='20' fill='#b0b6c1'/><ellipse cx='20' cy='20' rx='12' ry='8' fill='#23272a'/></svg> },
    ],
    animation: (
      <svg width="320" height="320" viewBox="0 0 320 320" fill="none"><ellipse cx="160" cy="160" rx="120" ry="80" fill="#6366f1" opacity="0.7" /><ellipse cx="160" cy="160" rx="60" ry="40" fill="#b0b6c1" opacity="0.5" /></svg>
    ),
  },
];

export default function ZigzagAnimatedSection() {
  const rowRefs = useRef([]);

  useEffect(() => {
    rowRefs.current.forEach((el, i) => {
      if (!el) return;
      gsap.fromTo(
        el,
        { opacity: 0, y: 100, x: i % 2 === 0 ? -100 : 100, scale: 0.95 },
        {
          opacity: 1,
          y: 0,
          x: 0,
          scale: 1,
          duration: 1.1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: el,
            start: "top 85%",
            toggleActions: "play none none none",
          },
        }
      );
    });
  }, []);

  // Use the same radial gradient as the Hero/global background
  const sectionBg = {
    background: "radial-gradient(ellipse at 30% 20%, #223040 60%, #181d20 100%)"
  };

  return (
    <section className="w-full" style={sectionBg}>
      {slides.map((slide, i) => (
        <div
          key={i}
          ref={el => (rowRefs.current[i] = el)}
          className={`relative flex flex-col md:flex-row items-center justify-center min-h-screen py-12 md:py-0 px-4 md:px-16 gap-10 md:gap-0`}
        >
          {/* Context */}
          <div className={`flex-1 flex flex-col justify-center items-${i % 2 === 0 ? 'start' : 'end'} z-10`}>
            <div className="bg-[#181d20]/80 rounded-2xl p-10 shadow-lg max-w-xl w-full backdrop-blur-md">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-100 mb-4 leading-tight">{slide.title}</h2>
              <p className="text-gray-300 text-lg mb-6">{slide.desc}</p>
              <div className="flex flex-wrap gap-4 mb-6">
                {slide.logos.map((logo, idx) => (
                  <div key={logo.name + idx} className="flex items-center gap-2 bg-[#23272a]/80 rounded-lg px-3 py-2 shadow">
                    {logo.svg}
                    <span className="text-gray-200 font-semibold text-base">{logo.name}</span>
                  </div>
                ))}
              </div>
              <button className="mt-2 px-6 py-2 rounded-full bg-indigo-600 text-white font-semibold shadow hover:bg-indigo-500 transition">{slide.cta}</button>
            </div>
          </div>
          {/* Animation */}
          <div className="flex-1 flex items-center justify-center z-10">
            <div className="rounded-2xl shadow-2xl bg-[#181d20]/80 p-6 md:p-12 flex items-center justify-center">
              {slide.animation}
            </div>
          </div>
          {/* Decorative overlay for depth */}
          <div className="absolute inset-0 pointer-events-none z-0">
            <svg width="100%" height="100%" viewBox="0 0 1440 900" fill="none" className="w-full h-full">
              <defs>
                <radialGradient id={`glow${i}`} cx="50%" cy="50%" r="70%">
                  <stop offset="0%" stopColor="#6366f1" stopOpacity="0.08" />
                  <stop offset="100%" stopColor="transparent" />
                </radialGradient>
              </defs>
              <rect width="1440" height="900" fill={`url(#glow${i})`} />
            </svg>
          </div>
        </div>
      ))}
    </section>
  );
} 