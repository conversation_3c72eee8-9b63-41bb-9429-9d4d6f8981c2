import React from 'react';

const useCases = [
  {
    title: "Content Creators",
    desc: "Transform podcasts, videos, and live streams into searchable text content. Perfect for show notes, blog posts, and social media.",
    icon: "🎬",
    features: ["Podcast transcription", "Video subtitles", "Social media content"]
  },
  {
    title: "Business Professionals",
    desc: "Convert meetings, interviews, and calls into actionable notes. Never miss important details or decisions again.",
    icon: "💼",
    features: ["Meeting minutes", "Interview notes", "Call summaries"]
  },
  {
    title: "Students & Researchers",
    desc: "Turn lectures, interviews, and research sessions into organized, searchable text for better study and analysis.",
    icon: "🎓",
    features: ["Lecture notes", "Research interviews", "Study materials"]
  },
  {
    title: "Journalists & Writers",
    desc: "Quickly transcribe interviews, press conferences, and voice memos to focus on crafting compelling stories.",
    icon: "✍️",
    features: ["Interview transcription", "Voice memos", "Press conferences"]
  }
];

export default function UseCasesSection() {
  return (
    <section className="w-full py-24 px-4 bg-gradient-to-b from-[#181d20]/30 to-transparent">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-100 mb-4">
            Perfect for <span className="text-indigo-400">Every Industry</span>
          </h2>
          <p className="text-gray-400 text-lg max-w-3xl mx-auto">
            From content creation to business meetings, our AI voice-to-text technology adapts to your specific needs and workflow.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {useCases.map((useCase, idx) => (
            <div key={idx} className="bg-[#23272a]/80 rounded-2xl p-8 border border-gray-700/50 hover:border-indigo-500/50 transition-all duration-300">
              <div className="flex items-start gap-4">
                <div className="text-4xl mb-4">{useCase.icon}</div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-gray-100 mb-3">{useCase.title}</h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">{useCase.desc}</p>
                  <div className="space-y-2">
                    {useCase.features.map((feature, i) => (
                      <div key={i} className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-indigo-400 rounded-full"></div>
                        <span className="text-gray-400">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}