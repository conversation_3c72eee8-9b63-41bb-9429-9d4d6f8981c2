import React from "react";

export default function ShowcaseSection() {
  return (
    <section className="w-full flex justify-center items-center py-20 px-4 bg-transparent">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-6xl">
        {/* Left Top Image */}
        <div className="rounded-2xl overflow-hidden bg-[#23272a] min-h-[260px] flex items-center justify-center">
          <img
            src="https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=600&q=80"
            alt="AI Voice Recognition"
            className="object-cover w-full h-full min-h-[260px]"
          />
        </div>
        {/* Feature Card */}
        <div className="rounded-2xl bg-gradient-to-b from-[#23272a] to-[#181d20] p-8 flex flex-col justify-center shadow-lg min-h-[260px]">
          <h3 className="text-2xl font-semibold text-gray-100 mb-2">AI Audio Recognition</h3>
          <p className="text-gray-300 mb-4">Experience the next generation of speech-to-text technology. Our platform delivers unmatched accuracy and speed, transforming spoken words into actionable text in real time.</p>
          <ul className="list-disc pl-5 text-gray-200 space-y-1">
            <li>Lightning-fast, real-time transcription</li>
            <li>Seamless integration with your workflow</li>
          </ul>
        </div>
        {/* Stat Cards */}
        <div className="flex gap-4 mt-0 md:mt-0">
          <div className="flex-1 rounded-2xl bg-[#23272a] p-8 flex flex-col items-center justify-center shadow text-center">
            <span className="text-3xl font-bold text-gray-100 mb-1">10,000+</span>
            <span className="text-gray-400 text-sm">Hours Transcribed</span>
          </div>
          <div className="flex-1 rounded-2xl bg-[#23272a] p-8 flex flex-col items-center justify-center shadow text-center">
            <span className="text-3xl font-bold text-gray-100 mb-1">2,500+</span>
            <span className="text-gray-400 text-sm">Active Users</span>
          </div>
        </div>
        {/* Right Bottom Image */}
        <div className="rounded-2xl overflow-hidden bg-[#23272a] min-h-[260px] flex items-center justify-center">
          <img
            src="https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&w=600&q=80"
            alt="User Collaboration"
            className="object-cover w-full h-full min-h-[260px]"
          />
        </div>
      </div>
    </section>
  );
} 